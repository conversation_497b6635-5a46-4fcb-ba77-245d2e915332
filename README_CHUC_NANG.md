# 🍜 Na Food - Tài liệu mô tả chi tiết các chức năng

## 📋 Tổng quan hệ thống

**Na Food** là một hệ thống đặt món ăn trực tuyến hoàn chỉnh được xây dựng với công nghệ full-stack JavaScript hiện đại. Hệ thống hỗ trợ đa vai trò người dùng và cung cấp trải nghiệm mượt mà từ việc duyệt món ăn đến thanh toán và theo dõi đơn hàng.

### 🏗️ Kiến trúc công nghệ
- **Frontend**: React 18 + Vite + TailwindCSS + Framer Motion
- **Backend**: Node.js + Express.js + MongoDB
- **Authentication**: JWT + bcrypt
- **UI Framework**: Radix UI + shadcn/ui components
- **State Management**: TanStack Query + React Context
- **Validation**: Zod schema validation
- **File Upload**: Multer với local storage
- **Charts**: Recharts cho visualization

---

## 👥 Hệ thống phân quyền người dùng

### 🔐 Admin (Quản trị viên)
**Quyền hạn cao nhất trong hệ thống**

#### Dashboard & Thống kê
- 📊 Xem tổng quan doanh thu theo ngày/tháng/năm
- 📈 Biểu đồ thống kê đơn hàng và doanh thu
- 🏆 Top sản phẩm bán chạy nhất
- 👥 Thống kê số lượng người dùng mới
- 📋 Xuất báo cáo CSV/Excel

#### Quản lý sản phẩm
- ➕ Thêm sản phẩm mới với đầy đủ thông tin
- ✏️ Chỉnh sửa thông tin sản phẩm (tên, giá, mô tả, hình ảnh)
- 🗑️ Xóa sản phẩm khỏi hệ thống
- 📸 Upload và quản lý hình ảnh sản phẩm
- 🏷️ Phân loại sản phẩm theo danh mục
- 🔄 Bật/tắt trạng thái hoạt động sản phẩm

#### Quản lý đơn hàng
- 📋 Xem tất cả đơn hàng trong hệ thống
- 🔍 Tìm kiếm đơn hàng theo ID, tên khách hàng, trạng thái
- 📅 Lọc đơn hàng theo ngày tạo
- 💳 Lọc theo phương thức thanh toán
- 🔄 Cập nhật trạng thái đơn hàng (Chờ xử lý → Đang xử lý → Đang giao → Đã giao → Hủy)
- 👁️ Xem chi tiết đơn hàng và thông tin khách hàng
- 📊 Xuất danh sách đơn hàng ra CSV

#### Quản lý người dùng
- 👥 Xem danh sách tất cả người dùng
- 🔍 Tìm kiếm người dùng theo email, tên
- 🔒 Khóa/mở khóa tài khoản người dùng
- 👨‍💼 Thay đổi vai trò người dùng (User ↔ Staff ↔ Admin)
- 📊 Xem thống kê hoạt động của người dùng

#### Quản lý đánh giá
- ⭐ Xem tất cả đánh giá sản phẩm
- ✅ Duyệt/từ chối đánh giá
- 🗑️ Xóa đánh giá không phù hợp
- 📊 Thống kê đánh giá theo sản phẩm
- 🔍 Tìm kiếm đánh giá theo sản phẩm, người dùng

#### Quản lý Banner
- 🖼️ Thêm/sửa/xóa banner trang chủ
- 📸 Upload hình ảnh banner
- 🔗 Thiết lập link điều hướng cho banner
- 📊 Sắp xếp thứ tự hiển thị banner
- 🔄 Bật/tắt trạng thái hiển thị banner

### 👨‍💼 Staff (Nhân viên)
**Chuyên xử lý đơn hàng và chăm sóc khách hàng**

#### Quản lý đơn hàng
- 📋 Xem danh sách đơn hàng được phân công
- 🔄 Cập nhật trạng thái đơn hàng
- 📞 Liên hệ khách hàng qua thông tin đơn hàng
- 📊 Xem thống kê đơn hàng cá nhân
- 🚚 Theo dõi tiến độ giao hàng

#### Dashboard Staff
- 📈 Thống kê đơn hàng đã xử lý
- ⏰ Đơn hàng chờ xử lý
- 🚚 Đơn hàng đang giao
- ✅ Đơn hàng đã hoàn thành

### 👤 Customer (Khách hàng)
**Trải nghiệm mua sắm hoàn chỉnh**

#### Duyệt sản phẩm
- 🍽️ Xem danh sách món ăn theo danh mục
- 🔍 Tìm kiếm món ăn theo tên
- 🏷️ Lọc theo danh mục (Món chính, Tráng miệng, Đồ uống, v.v.)
- 💰 Lọc theo khoảng giá
- ⭐ Xem đánh giá và rating của sản phẩm
- 📱 Giao diện responsive trên mọi thiết bị

#### Giỏ hàng
- 🛒 Thêm/xóa sản phẩm vào giỏ hàng
- 📊 Tăng/giảm số lượng sản phẩm
- 💾 Lưu trữ giỏ hàng tự động (localStorage)
- 💰 Tính tổng tiền tự động
- 🔄 Đồng bộ giỏ hàng real-time

#### Đặt hàng & Thanh toán
- 📝 Điền thông tin giao hàng
- 💳 Chọn phương thức thanh toán:
  - 💵 Thanh toán khi nhận hàng (COD)
  - 🏦 Chuyển khoản ngân hàng
  - 📱 Ví điện tử (MoMo, ZaloPay)
- ✅ Xác nhận đơn hàng
- 📧 Nhận thông báo xác nhận qua email

#### Theo dõi đơn hàng
- 📋 Xem lịch sử đơn hàng
- 🔍 Tìm kiếm đơn hàng theo ID
- 📊 Theo dõi trạng thái đơn hàng real-time
- 📅 Lọc đơn hàng theo ngày
- 💰 Xem chi tiết thanh toán

#### Đánh giá sản phẩm
- ⭐ Đánh giá sản phẩm (1-5 sao)
- 💬 Viết nhận xét chi tiết
- 📸 Upload hình ảnh đánh giá
- ✅ Chỉ đánh giá được sau khi đơn hàng hoàn thành

---

## 🛍️ Chức năng sản phẩm chi tiết

### 📦 Quản lý sản phẩm
- **Thông tin cơ bản**: Tên, mô tả, giá, danh mục
- **Hình ảnh**: Upload multiple images, auto-resize
- **Trạng thái**: Active/Inactive, Featured products
- **SEO**: Meta description, keywords
- **Inventory**: Theo dõi số lượng tồn kho

### 🏷️ Danh mục sản phẩm
- **Món chính**: Cơm, phở, bún, mì
- **Tráng miệng**: Chè, bánh ngọt, kem
- **Đồ uống**: Nước ngọt, trà, cà phê
- **Món ăn vặt**: Bánh tráng, nem chua
- **Combo**: Set meal với giá ưu đãi

---

## 🛒 Hệ thống giỏ hàng & Thanh toán

### 💾 Giỏ hàng thông minh
- **Persistent Storage**: Lưu trữ với localStorage
- **Real-time Updates**: Cập nhật tức thời
- **Quantity Management**: Tăng/giảm số lượng
- **Price Calculation**: Tính tổng tiền tự động
- **Item Validation**: Kiểm tra tồn kho trước khi checkout

### 💳 Phương thức thanh toán
1. **COD (Cash on Delivery)**
   - Thanh toán khi nhận hàng
   - Phí ship cố định
   - Xác nhận qua SMS

2. **Chuyển khoản ngân hàng**
   - QR Code thanh toán
   - Thông tin tài khoản ngân hàng
   - Xác nhận tự động

3. **Ví điện tử**
   - MoMo, ZaloPay integration
   - Thanh toán 1-click
   - Cashback rewards

---

## 📊 Hệ thống thống kê & Báo cáo

### 📈 Dashboard Analytics
- **Doanh thu**: Theo ngày/tuần/tháng/năm
- **Đơn hàng**: Số lượng, trạng thái, conversion rate
- **Sản phẩm**: Top sellers, slow movers
- **Khách hàng**: New vs returning customers
- **Biểu đồ**: Line charts, bar charts, pie charts

### 📋 Báo cáo xuất file
- **CSV Export**: Đơn hàng, sản phẩm, khách hàng
- **Excel Reports**: Báo cáo tổng hợp
- **PDF Invoices**: Hóa đơn đơn hàng
- **Email Reports**: Gửi báo cáo định kỳ

---

## ⭐ Hệ thống đánh giá & Review

### 🌟 Đánh giá sản phẩm
- **Rating Scale**: 1-5 sao
- **Written Reviews**: Nhận xét chi tiết
- **Photo Reviews**: Upload hình ảnh
- **Verified Purchase**: Chỉ khách đã mua mới được đánh giá
- **Moderation**: Admin duyệt trước khi hiển thị

### 📊 Thống kê đánh giá
- **Average Rating**: Điểm trung bình sản phẩm
- **Review Distribution**: Phân bố theo số sao
- **Review Trends**: Xu hướng đánh giá theo thời gian
- **Top Reviewers**: Khách hàng đánh giá nhiều nhất

---

## 🔐 Bảo mật & Authentication

### 🛡️ Hệ thống bảo mật
- **JWT Authentication**: Token-based auth
- **Password Hashing**: bcrypt encryption
- **Role-based Access**: Phân quyền chi tiết
- **Session Management**: Auto logout, remember me
- **CORS Protection**: Cross-origin security

### 🔑 Quản lý tài khoản
- **Registration**: Đăng ký với email verification
- **Login**: Email/password authentication
- **Password Reset**: Forgot password flow
- **Profile Management**: Cập nhật thông tin cá nhân
- **Account Security**: 2FA, login history

---

## 📱 Responsive Design & UX

### 🎨 Giao diện người dùng
- **Modern UI**: Clean, intuitive design
- **Dark/Light Mode**: Theme switching
- **Mobile First**: Responsive trên mọi thiết bị
- **Fast Loading**: Optimized performance
- **Accessibility**: WCAG compliant

### 🔔 Notifications & Alerts
- **Toast Messages**: Success/error notifications
- **Real-time Updates**: WebSocket connections
- **Email Notifications**: Order confirmations
- **Push Notifications**: Mobile app ready

---

## 🚀 Performance & Optimization

### ⚡ Tối ưu hiệu suất
- **Code Splitting**: Lazy loading components
- **Image Optimization**: Auto-resize, WebP format
- **Caching Strategy**: Browser & server caching
- **Database Indexing**: Optimized queries
- **CDN Integration**: Static asset delivery

### 📊 Monitoring & Analytics
- **Error Tracking**: Real-time error monitoring
- **Performance Metrics**: Page load times
- **User Analytics**: Behavior tracking
- **Server Monitoring**: Uptime, response times

---

## 🛠️ Công cụ phát triển & Deployment

### 🔧 Development Tools
- **Hot Reload**: Instant development feedback
- **ESLint/Prettier**: Code formatting
- **TypeScript**: Type safety (optional)
- **Testing**: Jest, React Testing Library
- **Storybook**: Component documentation

### 🚀 Deployment & DevOps
- **Docker**: Containerized deployment
- **CI/CD**: Automated testing & deployment
- **Environment Management**: Dev/staging/prod
- **Database Migrations**: Schema versioning
- **Backup Strategy**: Automated backups

---

## 📞 Hỗ trợ & Bảo trì

### 🔧 Maintenance Features
- **Health Checks**: System status monitoring
- **Database Cleanup**: Automated maintenance
- **Log Management**: Centralized logging
- **Update System**: Version management
- **Backup & Restore**: Data protection

### 📚 Documentation
- **API Documentation**: Comprehensive API docs
- **User Manual**: End-user guides
- **Developer Guide**: Technical documentation
- **Deployment Guide**: Setup instructions
- **Troubleshooting**: Common issues & solutions

---

## 🎯 Tính năng nâng cao

### 🤖 AI & Machine Learning
- **Recommendation Engine**: Gợi ý sản phẩm
- **Price Optimization**: Dynamic pricing
- **Demand Forecasting**: Inventory planning
- **Customer Segmentation**: Targeted marketing
- **Chatbot Support**: Automated customer service

### 🌐 Multi-language & Localization
- **Vietnamese**: Ngôn ngữ chính
- **English**: International support
- **Currency**: VND, USD support
- **Timezone**: Multiple timezone support
- **Cultural Adaptation**: Local preferences

---

*Tài liệu này mô tả chi tiết tất cả các chức năng hiện có trong hệ thống Na Food. Để biết thêm thông tin chi tiết về cách sử dụng, vui lòng tham khảo các file hướng dẫn khác trong dự án.*
