# 🍜 NAFOODLVN - SQL Server Database Schema

## 📋 Tổng quan
Database schema cho hệ thống quản lý nhà hàng NAFOODLVN được chuyển đổi từ MongoDB sang SQL Server, bao gồm đầy đủ các bảng, constraints, indexes, stored procedures và triggers.

## 🗄️ Tạo Database

```sql
-- Tạo database
CREATE DATABASE NAFOODLVN;
GO

USE NAFOODLVN;
GO

-- Tạo schema cho các module
CREATE SCHEMA [auth];
CREATE SCHEMA [product];
CREATE SCHEMA [order];
CREATE SCHEMA [review];
CREATE SCHEMA [content];
GO
```

## 📊 Tạo các bảng chính

### 1. Bảng Users - Quản lý người dùng

```sql
CREATE TABLE [auth].[Users] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [Username] NVARCHAR(50) NOT NULL,
    [Email] NVARCHAR(255) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [FullName] NVARCHAR(255) NOT NULL,
    [Phone] NVARCHAR(20) NULL,
    [Address] NVARCHAR(500) NULL,
    [Role] NVARCHAR(20) NOT NULL DEFAULT 'user' 
        CHECK ([Role] IN ('user', 'staff', 'admin')),
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    INDEX IX_Users_Email NONCLUSTERED ([Email]),
    INDEX IX_Users_Role NONCLUSTERED ([Role]),
    INDEX IX_Users_IsActive NONCLUSTERED ([IsActive])
);
GO
```

### 2. Bảng Products - Quản lý sản phẩm

```sql
CREATE TABLE [product].[Products] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [Name] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(1000) NULL,
    [Price] DECIMAL(18,2) NOT NULL CHECK ([Price] > 0),
    [Category] NVARCHAR(100) NOT NULL,
    [Image] NVARCHAR(500) NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    INDEX IX_Products_Category NONCLUSTERED ([Category]),
    INDEX IX_Products_IsActive NONCLUSTERED ([IsActive]),
    INDEX IX_Products_Name NONCLUSTERED ([Name])
);
GO
```

### 3. Bảng Orders - Quản lý đơn hàng

```sql
CREATE TABLE [order].[Orders] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [Total] DECIMAL(18,2) NOT NULL CHECK ([Total] > 0),
    [Status] NVARCHAR(20) NOT NULL DEFAULT 'pending'
        CHECK ([Status] IN ('pending', 'processing', 'shipping', 'delivered', 'cancelled')),
    [PaymentMethod] NVARCHAR(20) NOT NULL
        CHECK ([PaymentMethod] IN ('cod', 'bank_transfer', 'e_wallet')),
    [CustomerName] NVARCHAR(255) NOT NULL,
    [CustomerPhone] NVARCHAR(20) NOT NULL,
    [CustomerAddress] NVARCHAR(500) NOT NULL,
    [Notes] NVARCHAR(1000) NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_Orders_Users FOREIGN KEY ([UserId]) 
        REFERENCES [auth].[Users]([Id]) ON DELETE CASCADE,
    
    INDEX IX_Orders_UserId NONCLUSTERED ([UserId]),
    INDEX IX_Orders_Status NONCLUSTERED ([Status]),
    INDEX IX_Orders_CreatedAt NONCLUSTERED ([CreatedAt] DESC)
);
GO
```

### 4. Bảng OrderItems - Chi tiết đơn hàng

```sql
CREATE TABLE [order].[OrderItems] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [OrderId] INT NOT NULL,
    [ProductId] INT NOT NULL,
    [ProductName] NVARCHAR(255) NOT NULL, -- Snapshot tại thời điểm đặt hàng
    [ProductPrice] DECIMAL(18,2) NOT NULL CHECK ([ProductPrice] > 0),
    [Quantity] INT NOT NULL CHECK ([Quantity] > 0),
    [ProductImage] NVARCHAR(500) NULL,
    [Subtotal] AS ([ProductPrice] * [Quantity]) PERSISTED,
    
    CONSTRAINT FK_OrderItems_Orders FOREIGN KEY ([OrderId]) 
        REFERENCES [order].[Orders]([Id]) ON DELETE CASCADE,
    CONSTRAINT FK_OrderItems_Products FOREIGN KEY ([ProductId]) 
        REFERENCES [product].[Products]([Id]),
    
    INDEX IX_OrderItems_OrderId NONCLUSTERED ([OrderId]),
    INDEX IX_OrderItems_ProductId NONCLUSTERED ([ProductId])
);
GO
```

### 5. Bảng Reviews - Đánh giá sản phẩm

```sql
CREATE TABLE [review].[Reviews] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] INT NOT NULL,
    [ProductId] INT NOT NULL,
    [OrderId] INT NOT NULL,
    [Rating] INT NOT NULL CHECK ([Rating] BETWEEN 1 AND 5),
    [Comment] NVARCHAR(1000) NOT NULL,
    [Approved] BIT NULL, -- NULL = pending, 1 = approved, 0 = rejected
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_Reviews_Users FOREIGN KEY ([UserId]) 
        REFERENCES [auth].[Users]([Id]) ON DELETE CASCADE,
    CONSTRAINT FK_Reviews_Products FOREIGN KEY ([ProductId]) 
        REFERENCES [product].[Products]([Id]) ON DELETE CASCADE,
    CONSTRAINT FK_Reviews_Orders FOREIGN KEY ([OrderId]) 
        REFERENCES [order].[Orders]([Id]),
    
    -- Unique constraint: Một user chỉ review một sản phẩm trong một order
    CONSTRAINT UQ_Reviews_User_Product_Order UNIQUE ([UserId], [ProductId], [OrderId]),
    
    INDEX IX_Reviews_ProductId NONCLUSTERED ([ProductId]),
    INDEX IX_Reviews_Approved NONCLUSTERED ([Approved]),
    INDEX IX_Reviews_Rating NONCLUSTERED ([Rating])
);
GO
```

### 6. Bảng Banners - Quản lý banner

```sql
CREATE TABLE [content].[Banners] (
    [Id] INT IDENTITY(1,1) PRIMARY KEY,
    [Title] NVARCHAR(255) NOT NULL,
    [Description] NVARCHAR(1000) NULL,
    [Image] NVARCHAR(500) NOT NULL,
    [Link] NVARCHAR(500) NULL,
    [Order] INT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    INDEX IX_Banners_Order NONCLUSTERED ([Order]),
    INDEX IX_Banners_IsActive NONCLUSTERED ([IsActive])
);
GO
```

## 🔧 Triggers cho UpdatedAt

```sql
-- Trigger cho Users
CREATE TRIGGER TR_Users_UpdatedAt ON [auth].[Users]
AFTER UPDATE AS
BEGIN
    UPDATE [auth].[Users] 
    SET [UpdatedAt] = GETUTCDATE()
    FROM [auth].[Users] u
    INNER JOIN inserted i ON u.[Id] = i.[Id];
END;
GO

-- Trigger cho Products  
CREATE TRIGGER TR_Products_UpdatedAt ON [product].[Products]
AFTER UPDATE AS
BEGIN
    UPDATE [product].[Products] 
    SET [UpdatedAt] = GETUTCDATE()
    FROM [product].[Products] p
    INNER JOIN inserted i ON p.[Id] = i.[Id];
END;
GO

-- Trigger cho Orders
CREATE TRIGGER TR_Orders_UpdatedAt ON [order].[Orders]
AFTER UPDATE AS
BEGIN
    UPDATE [order].[Orders] 
    SET [UpdatedAt] = GETUTCDATE()
    FROM [order].[Orders] o
    INNER JOIN inserted i ON o.[Id] = i.[Id];
END;
GO

-- Trigger cho Reviews
CREATE TRIGGER TR_Reviews_UpdatedAt ON [review].[Reviews]
AFTER UPDATE AS
BEGIN
    UPDATE [review].[Reviews] 
    SET [UpdatedAt] = GETUTCDATE()
    FROM [review].[Reviews] r
    INNER JOIN inserted i ON r.[Id] = i.[Id];
END;
GO
```

## 📝 Stored Procedures

### 1. Authentication Procedures

```sql
-- Đăng ký người dùng mới
CREATE PROCEDURE [auth].[sp_RegisterUser]
    @Username NVARCHAR(50),
    @Email NVARCHAR(255),
    @PasswordHash NVARCHAR(255),
    @FullName NVARCHAR(255),
    @Phone NVARCHAR(20) = NULL,
    @Address NVARCHAR(500) = NULL,
    @Role NVARCHAR(20) = 'user'
AS
BEGIN
    SET NOCOUNT ON;

    -- Kiểm tra email đã tồn tại
    IF EXISTS (SELECT 1 FROM [auth].[Users] WHERE [Email] = @Email)
    BEGIN
        THROW 50001, 'Email đã được sử dụng', 1;
        RETURN;
    END

    INSERT INTO [auth].[Users] ([Username], [Email], [PasswordHash], [FullName], [Phone], [Address], [Role])
    VALUES (@Username, @Email, @PasswordHash, @FullName, @Phone, @Address, @Role);

    SELECT SCOPE_IDENTITY() AS NewUserId;
END;
GO

-- Đăng nhập
CREATE PROCEDURE [auth].[sp_LoginUser]
    @Email NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT [Id], [Email], [PasswordHash], [FullName], [Role], [IsActive]
    FROM [auth].[Users]
    WHERE [Email] = @Email AND [IsActive] = 1;
END;
GO
```

### 2. Product Management Procedures

```sql
-- Lấy danh sách sản phẩm với filter
CREATE PROCEDURE [product].[sp_GetProducts]
    @Category NVARCHAR(100) = NULL,
    @SearchTerm NVARCHAR(255) = NULL,
    @IsActive BIT = 1
AS
BEGIN
    SET NOCOUNT ON;

    SELECT [Id], [Name], [Description], [Price], [Category], [Image], [IsActive], [CreatedAt], [UpdatedAt]
    FROM [product].[Products]
    WHERE (@Category IS NULL OR [Category] = @Category)
        AND (@SearchTerm IS NULL OR [Name] LIKE '%' + @SearchTerm + '%')
        AND (@IsActive IS NULL OR [IsActive] = @IsActive)
    ORDER BY [CreatedAt] DESC;
END;
GO

-- Tạo sản phẩm mới
CREATE PROCEDURE [product].[sp_CreateProduct]
    @Name NVARCHAR(255),
    @Description NVARCHAR(1000) = NULL,
    @Price DECIMAL(18,2),
    @Category NVARCHAR(100),
    @Image NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO [product].[Products] ([Name], [Description], [Price], [Category], [Image])
    VALUES (@Name, @Description, @Price, @Category, @Image);

    SELECT SCOPE_IDENTITY() AS NewProductId;
END;
GO

-- Cập nhật sản phẩm
CREATE PROCEDURE [product].[sp_UpdateProduct]
    @Id INT,
    @Name NVARCHAR(255) = NULL,
    @Description NVARCHAR(1000) = NULL,
    @Price DECIMAL(18,2) = NULL,
    @Category NVARCHAR(100) = NULL,
    @Image NVARCHAR(500) = NULL,
    @IsActive BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [product].[Products]
    SET [Name] = ISNULL(@Name, [Name]),
        [Description] = ISNULL(@Description, [Description]),
        [Price] = ISNULL(@Price, [Price]),
        [Category] = ISNULL(@Category, [Category]),
        [Image] = ISNULL(@Image, [Image]),
        [IsActive] = ISNULL(@IsActive, [IsActive])
    WHERE [Id] = @Id;

    SELECT @@ROWCOUNT AS RowsAffected;
END;
GO
```

### 3. Order Management Procedures

```sql
-- Tạo đơn hàng mới
CREATE PROCEDURE [order].[sp_CreateOrder]
    @UserId INT,
    @Total DECIMAL(18,2),
    @PaymentMethod NVARCHAR(20),
    @CustomerName NVARCHAR(255),
    @CustomerPhone NVARCHAR(20),
    @CustomerAddress NVARCHAR(500),
    @Notes NVARCHAR(1000) = NULL,
    @OrderItems NVARCHAR(MAX) -- JSON string chứa danh sách items
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;

    DECLARE @OrderId INT;

    TRY
        -- Tạo đơn hàng
        INSERT INTO [order].[Orders] ([UserId], [Total], [PaymentMethod], [CustomerName], [CustomerPhone], [CustomerAddress], [Notes])
        VALUES (@UserId, @Total, @PaymentMethod, @CustomerName, @CustomerPhone, @CustomerAddress, @Notes);

        SET @OrderId = SCOPE_IDENTITY();

        -- Parse và insert order items từ JSON
        INSERT INTO [order].[OrderItems] ([OrderId], [ProductId], [ProductName], [ProductPrice], [Quantity], [ProductImage])
        SELECT
            @OrderId,
            JSON_VALUE(value, '$.productId'),
            JSON_VALUE(value, '$.name'),
            CAST(JSON_VALUE(value, '$.price') AS DECIMAL(18,2)),
            CAST(JSON_VALUE(value, '$.quantity') AS INT),
            JSON_VALUE(value, '$.image')
        FROM OPENJSON(@OrderItems);

        COMMIT TRANSACTION;
        SELECT @OrderId AS NewOrderId;

    CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH;
END;
GO

-- Cập nhật trạng thái đơn hàng
CREATE PROCEDURE [order].[sp_UpdateOrderStatus]
    @Id INT,
    @Status NVARCHAR(20)
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [order].[Orders]
    SET [Status] = @Status
    WHERE [Id] = @Id;

    SELECT @@ROWCOUNT AS RowsAffected;
END;
GO

-- Lấy đơn hàng với chi tiết
CREATE PROCEDURE [order].[sp_GetOrderWithItems]
    @OrderId INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Thông tin đơn hàng
    SELECT o.[Id], o.[UserId], o.[Total], o.[Status], o.[PaymentMethod],
           o.[CustomerName], o.[CustomerPhone], o.[CustomerAddress], o.[Notes],
           o.[CreatedAt], o.[UpdatedAt],
           u.[FullName] AS UserFullName, u.[Email] AS UserEmail
    FROM [order].[Orders] o
    INNER JOIN [auth].[Users] u ON o.[UserId] = u.[Id]
    WHERE o.[Id] = @OrderId;

    -- Chi tiết items
    SELECT [Id], [OrderId], [ProductId], [ProductName], [ProductPrice], [Quantity], [ProductImage], [Subtotal]
    FROM [order].[OrderItems]
    WHERE [OrderId] = @OrderId;
END;
GO
```

### 4. Review Management Procedures

```sql
-- Tạo review mới
CREATE PROCEDURE [review].[sp_CreateReview]
    @UserId INT,
    @ProductId INT,
    @OrderId INT,
    @Rating INT,
    @Comment NVARCHAR(1000)
AS
BEGIN
    SET NOCOUNT ON;

    -- Kiểm tra đơn hàng đã delivered và thuộc về user
    IF NOT EXISTS (
        SELECT 1 FROM [order].[Orders]
        WHERE [Id] = @OrderId AND [UserId] = @UserId AND [Status] = 'delivered'
    )
    BEGIN
        THROW 50002, 'Chỉ có thể đánh giá sản phẩm từ đơn hàng đã giao thành công', 1;
        RETURN;
    END

    -- Kiểm tra sản phẩm có trong đơn hàng
    IF NOT EXISTS (
        SELECT 1 FROM [order].[OrderItems]
        WHERE [OrderId] = @OrderId AND [ProductId] = @ProductId
    )
    BEGIN
        THROW 50003, 'Sản phẩm không có trong đơn hàng này', 1;
        RETURN;
    END

    INSERT INTO [review].[Reviews] ([UserId], [ProductId], [OrderId], [Rating], [Comment])
    VALUES (@UserId, @ProductId, @OrderId, @Rating, @Comment);

    SELECT SCOPE_IDENTITY() AS NewReviewId;
END;
GO

-- Duyệt/từ chối review
CREATE PROCEDURE [review].[sp_ApproveReview]
    @Id INT,
    @Approved BIT
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE [review].[Reviews]
    SET [Approved] = @Approved
    WHERE [Id] = @Id;

    SELECT @@ROWCOUNT AS RowsAffected;
END;
GO

-- Lấy reviews với filter
CREATE PROCEDURE [review].[sp_GetReviews]
    @ProductId INT = NULL,
    @UserId INT = NULL,
    @Approved BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT r.[Id], r.[UserId], r.[ProductId], r.[OrderId], r.[Rating], r.[Comment],
           r.[Approved], r.[CreatedAt], r.[UpdatedAt],
           u.[FullName] AS UserName, p.[Name] AS ProductName
    FROM [review].[Reviews] r
    INNER JOIN [auth].[Users] u ON r.[UserId] = u.[Id]
    INNER JOIN [product].[Products] p ON r.[ProductId] = p.[Id]
    WHERE (@ProductId IS NULL OR r.[ProductId] = @ProductId)
        AND (@UserId IS NULL OR r.[UserId] = @UserId)
        AND (@Approved IS NULL OR r.[Approved] = @Approved)
    ORDER BY r.[CreatedAt] DESC;
END;
GO
```

### 5. Statistics Procedures

```sql
-- Thống kê tổng quan
CREATE PROCEDURE [order].[sp_GetOrderStatistics]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        COUNT(*) AS TotalOrders,
        SUM(CASE WHEN [Status] = 'delivered' THEN [Total] ELSE 0 END) AS TotalRevenue,
        COUNT(DISTINCT [UserId]) AS TotalCustomers,
        AVG(CASE WHEN [Status] = 'delivered' THEN [Total] ELSE NULL END) AS AverageOrderValue,
        COUNT(CASE WHEN [Status] = 'pending' THEN 1 END) AS PendingOrders,
        COUNT(CASE WHEN [Status] = 'processing' THEN 1 END) AS ProcessingOrders,
        COUNT(CASE WHEN [Status] = 'shipping' THEN 1 END) AS ShippingOrders,
        COUNT(CASE WHEN [Status] = 'delivered' THEN 1 END) AS DeliveredOrders,
        COUNT(CASE WHEN [Status] = 'cancelled' THEN 1 END) AS CancelledOrders
    FROM [order].[Orders];
END;
GO

-- Top sản phẩm bán chạy
CREATE PROCEDURE [product].[sp_GetTopProducts]
    @Limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;

    SELECT TOP (@Limit)
        p.[Id], p.[Name], p.[Price], p.[Category], p.[Image],
        SUM(oi.[Quantity]) AS TotalQuantitySold,
        SUM(oi.[Subtotal]) AS TotalRevenue,
        COUNT(DISTINCT o.[Id]) AS TotalOrders,
        AVG(CAST(r.[Rating] AS FLOAT)) AS AverageRating,
        COUNT(r.[Id]) AS TotalReviews
    FROM [product].[Products] p
    INNER JOIN [order].[OrderItems] oi ON p.[Id] = oi.[ProductId]
    INNER JOIN [order].[Orders] o ON oi.[OrderId] = o.[Id] AND o.[Status] = 'delivered'
    LEFT JOIN [review].[Reviews] r ON p.[Id] = r.[ProductId] AND r.[Approved] = 1
    WHERE p.[IsActive] = 1
    GROUP BY p.[Id], p.[Name], p.[Price], p.[Category], p.[Image]
    ORDER BY TotalRevenue DESC;
END;
GO

-- Doanh thu theo thời gian
CREATE PROCEDURE [order].[sp_GetRevenueByPeriod]
    @StartDate DATE,
    @EndDate DATE,
    @GroupBy NVARCHAR(10) = 'day' -- 'day', 'week', 'month'
AS
BEGIN
    SET NOCOUNT ON;

    IF @GroupBy = 'day'
    BEGIN
        SELECT
            CAST([CreatedAt] AS DATE) AS Period,
            COUNT(*) AS OrderCount,
            SUM([Total]) AS Revenue
        FROM [order].[Orders]
        WHERE [Status] = 'delivered'
            AND CAST([CreatedAt] AS DATE) BETWEEN @StartDate AND @EndDate
        GROUP BY CAST([CreatedAt] AS DATE)
        ORDER BY Period;
    END
    ELSE IF @GroupBy = 'month'
    BEGIN
        SELECT
            FORMAT([CreatedAt], 'yyyy-MM') AS Period,
            COUNT(*) AS OrderCount,
            SUM([Total]) AS Revenue
        FROM [order].[Orders]
        WHERE [Status] = 'delivered'
            AND CAST([CreatedAt] AS DATE) BETWEEN @StartDate AND @EndDate
        GROUP BY FORMAT([CreatedAt], 'yyyy-MM')
        ORDER BY Period;
    END
END;
GO
```

## 🔍 Functions và Views

### 1. Utility Functions

```sql
-- Function tính tổng đánh giá trung bình của sản phẩm
CREATE FUNCTION [product].[fn_GetProductAverageRating](@ProductId INT)
RETURNS DECIMAL(3,2)
AS
BEGIN
    DECLARE @AvgRating DECIMAL(3,2);

    SELECT @AvgRating = AVG(CAST([Rating] AS DECIMAL(3,2)))
    FROM [review].[Reviews]
    WHERE [ProductId] = @ProductId AND [Approved] = 1;

    RETURN ISNULL(@AvgRating, 0);
END;
GO

-- Function kiểm tra user có thể review sản phẩm không
CREATE FUNCTION [review].[fn_CanUserReviewProduct](@UserId INT, @ProductId INT, @OrderId INT)
RETURNS BIT
AS
BEGIN
    DECLARE @CanReview BIT = 0;

    -- Kiểm tra đơn hàng đã delivered và chứa sản phẩm
    IF EXISTS (
        SELECT 1
        FROM [order].[Orders] o
        INNER JOIN [order].[OrderItems] oi ON o.[Id] = oi.[OrderId]
        WHERE o.[Id] = @OrderId
            AND o.[UserId] = @UserId
            AND o.[Status] = 'delivered'
            AND oi.[ProductId] = @ProductId
    )
    AND NOT EXISTS (
        SELECT 1 FROM [review].[Reviews]
        WHERE [UserId] = @UserId AND [ProductId] = @ProductId AND [OrderId] = @OrderId
    )
    BEGIN
        SET @CanReview = 1;
    END

    RETURN @CanReview;
END;
GO
```

### 2. Views cho báo cáo

```sql
-- View sản phẩm với thông tin đánh giá
CREATE VIEW [product].[vw_ProductsWithRating]
AS
SELECT
    p.[Id], p.[Name], p.[Description], p.[Price], p.[Category], p.[Image], p.[IsActive],
    p.[CreatedAt], p.[UpdatedAt],
    ISNULL(AVG(CAST(r.[Rating] AS FLOAT)), 0) AS AverageRating,
    COUNT(r.[Id]) AS TotalReviews,
    SUM(CASE WHEN r.[Rating] = 5 THEN 1 ELSE 0 END) AS FiveStarCount,
    SUM(CASE WHEN r.[Rating] = 4 THEN 1 ELSE 0 END) AS FourStarCount,
    SUM(CASE WHEN r.[Rating] = 3 THEN 1 ELSE 0 END) AS ThreeStarCount,
    SUM(CASE WHEN r.[Rating] = 2 THEN 1 ELSE 0 END) AS TwoStarCount,
    SUM(CASE WHEN r.[Rating] = 1 THEN 1 ELSE 0 END) AS OneStarCount
FROM [product].[Products] p
LEFT JOIN [review].[Reviews] r ON p.[Id] = r.[ProductId] AND r.[Approved] = 1
GROUP BY p.[Id], p.[Name], p.[Description], p.[Price], p.[Category], p.[Image],
         p.[IsActive], p.[CreatedAt], p.[UpdatedAt];
GO

-- View đơn hàng với thông tin chi tiết
CREATE VIEW [order].[vw_OrdersWithDetails]
AS
SELECT
    o.[Id], o.[UserId], o.[Total], o.[Status], o.[PaymentMethod],
    o.[CustomerName], o.[CustomerPhone], o.[CustomerAddress], o.[Notes],
    o.[CreatedAt], o.[UpdatedAt],
    u.[FullName] AS UserFullName, u.[Email] AS UserEmail, u.[Role] AS UserRole,
    COUNT(oi.[Id]) AS TotalItems,
    SUM(oi.[Quantity]) AS TotalQuantity
FROM [order].[Orders] o
INNER JOIN [auth].[Users] u ON o.[UserId] = u.[Id]
LEFT JOIN [order].[OrderItems] oi ON o.[Id] = oi.[OrderId]
GROUP BY o.[Id], o.[UserId], o.[Total], o.[Status], o.[PaymentMethod],
         o.[CustomerName], o.[CustomerPhone], o.[CustomerAddress], o.[Notes],
         o.[CreatedAt], o.[UpdatedAt], u.[FullName], u.[Email], u.[Role];
GO
```

## 📊 Sample Data

```sql
-- Insert sample users
INSERT INTO [auth].[Users] ([Username], [Email], [PasswordHash], [FullName], [Phone], [Address], [Role])
VALUES
    ('admin', '<EMAIL>', '$2b$10$hashedpassword1', N'Quản trị viên', '0123456789', N'123 Đường ABC, Quận 1, TP.HCM', 'admin'),
    ('staff1', '<EMAIL>', '$2b$10$hashedpassword2', N'Nhân viên 1', '0987654321', N'456 Đường DEF, Quận 2, TP.HCM', 'staff'),
    ('user1', '<EMAIL>', '$2b$10$hashedpassword3', N'Nguyễn Văn A', '0111222333', N'789 Đường GHI, Quận 3, TP.HCM', 'user');

-- Insert sample products
INSERT INTO [product].[Products] ([Name], [Description], [Price], [Category], [Image])
VALUES
    (N'Phở Bò Tái', N'Phở bò tái truyền thống với nước dúng trong, thịt bò tái mềm ngon', 65000, N'Món chính', '/images/pho-bo-tai.jpg'),
    (N'Bún Bò Huế', N'Bún bò Huế cay nồng đậm đà hương vị miền Trung', 70000, N'Món chính', '/images/bun-bo-hue.jpg'),
    (N'Cơm Tấm Sườn Nướng', N'Cơm tấm sườn nướng thơm lừng, ăn kèm chả trứng và bì', 55000, N'Món chính', '/images/com-tam-suon-nuong.jpg'),
    (N'Bánh Mì Thịt Nướng', N'Bánh mì Việt Nam với thịt nướng thơm ngon, rau sống tươi mát', 25000, N'Món nhẹ', '/images/banh-mi-thit-nuong.jpg'),
    (N'Chè Ba Màu', N'Chè ba màu truyền thống với đậu xanh, đậu đỏ và thạch', 20000, N'Món tráng miệng', '/images/che-ba-mau.jpg'),
    (N'Trà Đá Chanh', N'Trà đá chanh tươi mát, giải khát tuyệt vời', 15000, N'Đồ uống', '/images/tra-da-chanh.jpg'),
    (N'Cà Phê Sữa Đá', N'Cà phê sữa đá đậm đà hương vị Việt Nam', 18000, N'Đồ uống', '/images/ca-phe-sua-da.jpg');

-- Insert sample banners
INSERT INTO [content].[Banners] ([Title], [Description], [Image], [Link], [Order])
VALUES
    (N'Thưởng thức món ngon mỗi ngày', N'Đặt món yêu thích của bạn chỉ với vài cú click', '/images/hero-banner.jpg', '#menu', 1);
GO

## 🔐 Security và Permissions

```sql
-- Tạo roles cho ứng dụng
CREATE ROLE [NaFoodApp];
CREATE ROLE [NaFoodAdmin];
CREATE ROLE [NaFoodStaff];

-- Cấp quyền cho role ứng dụng
GRANT SELECT, INSERT, UPDATE ON [auth].[Users] TO [NaFoodApp];
GRANT SELECT, INSERT, UPDATE ON [product].[Products] TO [NaFoodApp];
GRANT SELECT, INSERT, UPDATE ON [order].[Orders] TO [NaFoodApp];
GRANT SELECT, INSERT, UPDATE ON [order].[OrderItems] TO [NaFoodApp];
GRANT SELECT, INSERT, UPDATE ON [review].[Reviews] TO [NaFoodApp];
GRANT SELECT ON [content].[Banners] TO [NaFoodApp];

-- Cấp quyền execute stored procedures
GRANT EXECUTE ON SCHEMA::[auth] TO [NaFoodApp];
GRANT EXECUTE ON SCHEMA::[product] TO [NaFoodApp];
GRANT EXECUTE ON SCHEMA::[order] TO [NaFoodApp];
GRANT EXECUTE ON SCHEMA::[review] TO [NaFoodApp];

-- Cấp quyền admin
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::[auth] TO [NaFoodAdmin];
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::[product] TO [NaFoodAdmin];
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::[order] TO [NaFoodAdmin];
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::[review] TO [NaFoodAdmin];
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::[content] TO [NaFoodAdmin];
GO
```

## 📈 Performance Indexes

```sql
-- Composite indexes cho queries phức tạp
CREATE NONCLUSTERED INDEX IX_Orders_UserId_Status_CreatedAt
ON [order].[Orders] ([UserId], [Status])
INCLUDE ([CreatedAt], [Total]);

CREATE NONCLUSTERED INDEX IX_OrderItems_ProductId_OrderId
ON [order].[OrderItems] ([ProductId], [OrderId])
INCLUDE ([Quantity], [Subtotal]);

CREATE NONCLUSTERED INDEX IX_Reviews_ProductId_Approved_Rating
ON [review].[Reviews] ([ProductId], [Approved])
INCLUDE ([Rating], [CreatedAt]);

CREATE NONCLUSTERED INDEX IX_Products_Category_IsActive_Price
ON [product].[Products] ([Category], [IsActive])
INCLUDE ([Price], [Name]);

-- Full-text search index cho tìm kiếm sản phẩm
CREATE FULLTEXT CATALOG [NaFoodCatalog];

CREATE FULLTEXT INDEX ON [product].[Products] ([Name], [Description])
KEY INDEX PK__Products__3214EC0743D61337
ON [NaFoodCatalog];
GO
```

## 🔄 Maintenance Scripts

```sql
-- Script backup database
CREATE PROCEDURE [dbo].[sp_BackupDatabase]
    @BackupPath NVARCHAR(500) = 'C:\Backup\NAFOODLVN_'
AS
BEGIN
    DECLARE @BackupFile NVARCHAR(500);
    DECLARE @DateTime NVARCHAR(20) = FORMAT(GETDATE(), 'yyyyMMdd_HHmmss');

    SET @BackupFile = @BackupPath + @DateTime + '.bak';

    BACKUP DATABASE [NAFOODLVN]
    TO DISK = @BackupFile
    WITH FORMAT, INIT, COMPRESSION;

    PRINT 'Backup completed: ' + @BackupFile;
END;
GO

-- Script dọn dệp dữ liệu cũ
CREATE PROCEDURE [dbo].[sp_CleanupOldData]
    @DaysToKeep INT = 365
AS
BEGIN
    DECLARE @CutoffDate DATETIME2 = DATEADD(DAY, -@DaysToKeep, GETUTCDATE());

    -- Xóa orders cũ đã cancelled
    DELETE FROM [order].[Orders]
    WHERE [Status] = 'cancelled' AND [CreatedAt] < @CutoffDate;

    -- Xóa reviews chưa được duyệt quá 30 ngày
    DELETE FROM [review].[Reviews]
    WHERE [Approved] IS NULL AND [CreatedAt] < DATEADD(DAY, -30, GETUTCDATE());

    PRINT 'Cleanup completed for data older than ' + CAST(@DaysToKeep AS NVARCHAR(10)) + ' days';
END;
GO
```

## 📋 Checklist triển khai

### ✅ Các bước cần thực hiện:

1. **Tạo database và schema**
   - [ ] Chạy script tạo database
   - [ ] Tạo các schema: auth, product, order, review, content

2. **Tạo tables và constraints**
   - [ ] Tạo tất cả bảng theo thứ tự (Users → Products → Orders → OrderItems → Reviews → Banners)
   - [ ] Kiểm tra foreign key constraints
   - [ ] Tạo indexes cơ bản

3. **Tạo stored procedures và functions**
   - [ ] Authentication procedures
   - [ ] Product management procedures
   - [ ] Order management procedures
   - [ ] Review procedures
   - [ ] Statistics procedures
   - [ ] Utility functions

4. **Tạo views và indexes nâng cao**
   - [ ] Views cho báo cáo
   - [ ] Performance indexes
   - [ ] Full-text search indexes

5. **Security và permissions**
   - [ ] Tạo database roles
   - [ ] Cấp quyền phù hợp
   - [ ] Tạo application users

6. **Sample data và testing**
   - [ ] Insert sample data
   - [ ] Test các stored procedures
   - [ ] Kiểm tra performance

7. **Backup và maintenance**
   - [ ] Setup backup schedule
   - [ ] Tạo maintenance plans
   - [ ] Monitor performance

### 🔧 Cấu hình khuyến nghị:

- **Recovery Model:** FULL (cho production)
- **Auto Growth:** 10% cho data, 10MB cho log
- **Max Memory:** 80% RAM server
- **Backup Schedule:** Full backup hàng ngày, Log backup mỗi 15 phút

---

*Database schema này được thiết kế dựa trên API của hệ thống NAFOODLVN, đảm bảo tính nhất quán dữ liệu, hiệu suất cao và khả năng mở rộng.*
```
```
```
