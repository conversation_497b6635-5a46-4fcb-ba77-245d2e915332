# 🔌 Na Food - API Documentation

## 📋 Tổng quan API

**Na Food API** là một RESTful API được xây dựng theo chuẩn REST, cung cấp đầy đủ các endpoint để quản lý hệ thống đặt món ăn trực tuyến. API sử dụng JSON cho tất cả request và response, với authentication dựa trên JWT token.

### 🌐 Base URL
```
http://localhost:5000/api
```

### 🔐 Authentication
API sử dụng JWT (JSON Web Token) để xác thực. Token được gửi trong header:
```
Authorization: Bearer <jwt_token>
```

### 📊 HTTP Status Codes
- `200` - OK: Request thành công
- `201` - Created: Tạo resource thành công
- `400` - Bad Request: Dữ liệu không hợp lệ
- `401` - Unauthorized: <PERSON><PERSON><PERSON> xác thực
- `403` - Forbidden: <PERSON><PERSON><PERSON><PERSON> c<PERSON> quyền truy cập
- `404` - Not Found: <PERSON>h<PERSON>ng tìm thấy resource
- `500` - Internal Server Error: Lỗi server

---

## 🏥 Health Check

### GET /api/health
Kiểm tra trạng thái hoạt động của server.

**Authentication:** Không yêu cầu

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "uptime": 3600.5
}
```

---

## 🔐 Authentication APIs

### POST /api/auth/register
Đăng ký tài khoản mới.

**Authentication:** Không yêu cầu

**Request Body:**
```json
{
  "fullName": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456"
}
```

**Response (201):**
```json
{
  "message": "Đăng ký thành công",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "fullName": "Nguyễn Văn A",
    "role": "user"
  }
}
```

**Validation Rules:**
- `fullName`: Bắt buộc, tối thiểu 1 ký tự
- `email`: Bắt buộc, định dạng email hợp lệ
- `password`: Bắt buộc, tối thiểu 6 ký tự
- `confirmPassword`: Phải khớp với password

### POST /api/auth/login
Đăng nhập vào hệ thống.

**Authentication:** Không yêu cầu

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**Response (200):**
```json
{
  "message": "Đăng nhập thành công",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "fullName": "Nguyễn Văn A",
    "role": "user"
  }
}
```

### GET /api/auth/test
Test authentication token.

**Authentication:** Yêu cầu JWT token

**Response (200):**
```json
{
  "message": "Authentication successful",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "role": "user"
  }
}
```

---

## 🍽️ Product APIs

### GET /api/products
Lấy danh sách sản phẩm.

**Authentication:** Không yêu cầu

**Query Parameters:**
- `category` (optional): Lọc theo danh mục
- `search` (optional): Tìm kiếm theo tên sản phẩm

**Example:**
```
GET /api/products?category=main-dish&search=phở
```

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "Phở Bò",
    "description": "Phở bò truyền thống Hà Nội",
    "price": 45000,
    "image": "/uploads/pho-bo.jpg",
    "category": "main-dish",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### GET /api/products/:id
Lấy thông tin chi tiết sản phẩm.

**Authentication:** Không yêu cầu

**Path Parameters:**
- `id`: ID của sản phẩm (integer)

**Response (200):**
```json
{
  "id": 1,
  "name": "Phở Bò",
  "description": "Phở bò truyền thống Hà Nội",
  "price": 45000,
  "image": "/uploads/pho-bo.jpg",
  "category": "main-dish",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

### POST /api/products
Tạo sản phẩm mới.

**Authentication:** Yêu cầu JWT token (Admin only)

**Request Body:**
```json
{
  "name": "Phở Gà",
  "description": "Phở gà thơm ngon",
  "price": 40000,
  "image": "/uploads/pho-ga.jpg",
  "category": "main-dish",
  "isActive": true
}
```

**Response (201):**
```json
{
  "id": 2,
  "name": "Phở Gà",
  "description": "Phở gà thơm ngon",
  "price": 40000,
  "image": "/uploads/pho-ga.jpg",
  "category": "main-dish",
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

### PUT /api/products/:id
Cập nhật thông tin sản phẩm.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của sản phẩm (integer)

**Request Body:** (Partial update)
```json
{
  "name": "Phở Gà Đặc Biệt",
  "price": 45000
}
```

**Response (200):**
```json
{
  "id": 2,
  "name": "Phở Gà Đặc Biệt",
  "description": "Phở gà thơm ngon",
  "price": 45000,
  "image": "/uploads/pho-ga.jpg",
  "category": "main-dish",
  "isActive": true,
  "updatedAt": "2024-01-15T11:30:00.000Z"
}
```

### DELETE /api/products/:id
Xóa sản phẩm.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của sản phẩm (integer)

**Response (200):**
```json
{
  "message": "Xóa sản phẩm thành công"
}
```

---

## 📦 Order APIs

### GET /api/orders
Lấy danh sách đơn hàng.

**Authentication:** Yêu cầu JWT token

**Query Parameters:**
- `status` (optional): Lọc theo trạng thái đơn hàng

**Behavior:**
- User: Chỉ xem đơn hàng của mình
- Admin/Staff: Xem tất cả đơn hàng

**Response (200):**
```json
[
  {
    "id": 1,
    "userId": 1,
    "total": 90000,
    "status": "pending",
    "paymentMethod": "cod",
    "customerName": "Nguyễn Văn A",
    "customerPhone": "**********",
    "customerAddress": "123 Đường ABC, Quận 1, TP.HCM",
    "items": [
      {
        "productId": 1,
        "name": "Phở Bò",
        "price": 45000,
        "quantity": 2,
        "image": "/uploads/pho-bo.jpg"
      }
    ],
    "notes": "Không hành",
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### GET /api/orders/:id
Lấy thông tin chi tiết đơn hàng.

**Authentication:** Yêu cầu JWT token

**Path Parameters:**
- `id`: ID của đơn hàng (integer)

**Authorization:**
- User: Chỉ xem đơn hàng của mình
- Admin/Staff: Xem tất cả đơn hàng

**Response (200):**
```json
{
  "id": 1,
  "userId": 1,
  "total": 90000,
  "status": "pending",
  "paymentMethod": "cod",
  "customerName": "Nguyễn Văn A",
  "customerPhone": "**********",
  "customerAddress": "123 Đường ABC, Quận 1, TP.HCM",
  "items": [
    {
      "productId": 1,
      "name": "Phở Bò",
      "price": 45000,
      "quantity": 2,
      "image": "/uploads/pho-bo.jpg"
    }
  ],
  "notes": "Không hành",
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

### POST /api/orders
Tạo đơn hàng mới.

**Authentication:** Yêu cầu JWT token

**Request Body:**
```json
{
  "total": 90000,
  "paymentMethod": "cod",
  "customerName": "Nguyễn Văn A",
  "customerPhone": "**********",
  "customerAddress": "123 Đường ABC, Quận 1, TP.HCM",
  "items": [
    {
      "productId": 1,
      "name": "Phở Bò",
      "price": 45000,
      "quantity": 2,
      "image": "/uploads/pho-bo.jpg"
    }
  ],
  "notes": "Không hành"
}
```

**Response (201):**
```json
{
  "id": 1,
  "userId": 1,
  "total": 90000,
  "status": "pending",
  "paymentMethod": "cod",
  "customerName": "Nguyễn Văn A",
  "customerPhone": "**********",
  "customerAddress": "123 Đường ABC, Quận 1, TP.HCM",
  "items": [
    {
      "productId": 1,
      "name": "Phở Bò",
      "price": 45000,
      "quantity": 2,
      "image": "/uploads/pho-bo.jpg"
    }
  ],
  "notes": "Không hành",
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

**Validation Rules:**
- `total`: Bắt buộc, số dương
- `paymentMethod`: Bắt buộc, enum ["cod", "bank_transfer", "e_wallet"]
- `customerName`: Bắt buộc, tối thiểu 1 ký tự
- `customerPhone`: Bắt buộc, tối thiểu 1 ký tự
- `customerAddress`: Bắt buộc, tối thiểu 1 ký tự
- `items`: Bắt buộc, array không rỗng

---

## 📁 File Upload API

### POST /api/upload
Upload file (hình ảnh).

**Authentication:** Yêu cầu JWT token

**Request:** Multipart form data
- `file`: File cần upload (image formats)

**Response (200):**
```json
{
  "success": true,
  "url": "/uploads/image-123456.jpg",
  "filename": "image-123456.jpg",
  "originalName": "my-image.jpg",
  "size": 1024000
}
```

**Supported formats:** JPG, JPEG, PNG, GIF, WebP
**Max file size:** 5MB
**Upload directory:** `/public/uploads/`

---

## 📊 Statistics APIs

### GET /api/statistics/overview
Lấy thống kê tổng quan.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Response (200):**
```json
{
  "todayRevenue": 1500000,
  "todayOrders": 25,
  "totalUsers": 150,
  "totalProducts": 45,
  "revenueGrowth": 12.5,
  "orderGrowth": 8.3
}
```

### GET /api/statistics/top-products
Lấy danh sách sản phẩm bán chạy.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Query Parameters:**
- `limit` (optional): Số lượng sản phẩm trả về (default: 10)

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "Phở Bò",
    "price": 45000,
    "image": "/uploads/pho-bo.jpg",
    "category": "main-dish",
    "orderCount": 150
  }
]
```

---

## 🎯 Error Responses

### Validation Error (400)
```json
{
  "message": "Dữ liệu không hợp lệ",
  "errors": [
    {
      "path": ["email"],
      "message": "Email không hợp lệ"
    }
  ]
}
```

### Authentication Error (401)
```json
{
  "message": "Access token required"
}
```

### Authorization Error (403)
```json
{
  "message": "Không có quyền truy cập"
}
```

### Not Found Error (404)
```json
{
  "message": "Không tìm thấy sản phẩm"
}
```

### Server Error (500)
```json
{
  "message": "Lỗi hệ thống"
}
```

---

## 🔒 Role-based Access Control

### Roles
- **user**: Khách hàng thông thường
- **staff**: Nhân viên
- **admin**: Quản trị viên

### Permissions Matrix
| Endpoint | User | Staff | Admin |
|----------|------|-------|-------|
| GET /api/products | ✅ | ✅ | ✅ |
| POST /api/products | ❌ | ❌ | ✅ |
| PUT /api/products/:id | ❌ | ❌ | ✅ |
| DELETE /api/products/:id | ❌ | ❌ | ✅ |
| GET /api/orders | ✅* | ✅ | ✅ |
| POST /api/orders | ✅ | ✅ | ✅ |
| PUT /api/orders/:id | ❌ | ✅ | ✅ |
| DELETE /api/orders/:id | ❌ | ❌ | ✅ |
| GET /api/statistics/* | ❌ | ✅ | ✅ |

*User chỉ xem được đơn hàng của mình

---

## 📝 Review APIs

### GET /api/reviews
Lấy danh sách đánh giá.

**Authentication:** Không yêu cầu

**Query Parameters:**
- `productId` (optional): Lọc theo sản phẩm
- `approved` (optional): Lọc theo trạng thái duyệt ("true", "false", "")

**Response (200):**
```json
[
  {
    "id": 1,
    "userId": 1,
    "productId": 1,
    "orderId": 1,
    "rating": 5,
    "comment": "Phở rất ngon!",
    "approved": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "user": {
      "fullName": "Nguyễn Văn A"
    },
    "product": {
      "name": "Phở Bò"
    }
  }
]
```

### POST /api/reviews
Tạo đánh giá mới.

**Authentication:** Yêu cầu JWT token

**Request Body:**
```json
{
  "productId": 1,
  "orderId": 1,
  "rating": 5,
  "comment": "Phở rất ngon!"
}
```

**Response (201):**
```json
{
  "id": 1,
  "userId": 1,
  "productId": 1,
  "orderId": 1,
  "rating": 5,
  "comment": "Phở rất ngon!",
  "approved": null,
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

**Validation Rules:**
- `productId`: Bắt buộc, integer
- `orderId`: Bắt buộc, integer
- `rating`: Bắt buộc, integer từ 1-5
- `comment`: Tùy chọn, string

### PUT /api/reviews/:id/approve
Duyệt đánh giá.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của đánh giá (integer)

**Response (200):**
```json
{
  "message": "Đánh giá đã được duyệt"
}
```

### PUT /api/reviews/:id/reject
Từ chối đánh giá.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của đánh giá (integer)

**Response (200):**
```json
{
  "message": "Đánh giá đã bị từ chối"
}
```

### DELETE /api/reviews/:id
Xóa đánh giá.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của đánh giá (integer)

**Response (200):**
```json
{
  "message": "Xóa đánh giá thành công"
}
```

---

## 🖼️ Banner APIs

### GET /api/banners
Lấy danh sách banner.

**Authentication:** Không yêu cầu

**Response (200):**
```json
[
  {
    "id": 1,
    "title": "Khuyến mãi đặc biệt",
    "description": "Giảm giá 20% cho tất cả món ăn",
    "image": "/uploads/banner-1.jpg",
    "link": "/products?category=promotion",
    "order": 1,
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### POST /api/banners
Tạo banner mới.

**Authentication:** Yêu cầu JWT token (Admin only)

**Request Body:**
```json
{
  "title": "Khuyến mãi mới",
  "description": "Giảm giá 30%",
  "image": "/uploads/banner-2.jpg",
  "link": "/products",
  "order": 2,
  "isActive": true
}
```

**Response (201):**
```json
{
  "id": 2,
  "title": "Khuyến mãi mới",
  "description": "Giảm giá 30%",
  "image": "/uploads/banner-2.jpg",
  "link": "/products",
  "order": 2,
  "isActive": true,
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

### PUT /api/banners/:id
Cập nhật banner.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của banner (integer)

**Request Body:** (Partial update)
```json
{
  "title": "Khuyến mãi cập nhật",
  "isActive": false
}
```

**Response (200):**
```json
{
  "id": 2,
  "title": "Khuyến mãi cập nhật",
  "description": "Giảm giá 30%",
  "image": "/uploads/banner-2.jpg",
  "link": "/products",
  "order": 2,
  "isActive": false,
  "updatedAt": "2024-01-15T11:30:00.000Z"
}
```

### DELETE /api/banners/:id
Xóa banner.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của banner (integer)

**Response (200):**
```json
{
  "message": "Xóa banner thành công"
}
```

---

## 👥 User Management APIs

### GET /api/users
Lấy danh sách người dùng.

**Authentication:** Yêu cầu JWT token (Admin only)

**Query Parameters:**
- `search` (optional): Tìm kiếm theo email hoặc tên
- `role` (optional): Lọc theo vai trò

**Response (200):**
```json
[
  {
    "id": 1,
    "username": "user1",
    "email": "<EMAIL>",
    "fullName": "Nguyễn Văn A",
    "phone": "**********",
    "address": "123 Đường ABC",
    "role": "user",
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### PUT /api/users/:id
Cập nhật thông tin người dùng.

**Authentication:** Yêu cầu JWT token (Admin only)

**Path Parameters:**
- `id`: ID của người dùng (integer)

**Request Body:** (Partial update)
```json
{
  "role": "staff",
  "isActive": false
}
```

**Response (200):**
```json
{
  "id": 1,
  "username": "user1",
  "email": "<EMAIL>",
  "fullName": "Nguyễn Văn A",
  "role": "staff",
  "isActive": false,
  "updatedAt": "2024-01-15T11:30:00.000Z"
}
```

---

## 📤 Export APIs

### GET /api/orders/export/csv
Xuất danh sách đơn hàng ra CSV.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Query Parameters:**
- `startDate` (optional): Ngày bắt đầu (YYYY-MM-DD)
- `endDate` (optional): Ngày kết thúc (YYYY-MM-DD)
- `status` (optional): Lọc theo trạng thái

**Response (200):**
```
Content-Type: text/csv
Content-Disposition: attachment; filename="orders.csv"

ID,Customer Name,Total,Status,Created At
1,Nguyễn Văn A,90000,pending,2024-01-15T10:30:00.000Z
```

### GET /api/orders/export/pdf
Xuất báo cáo đơn hàng ra PDF.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Status:** 501 Not Implemented (Đang phát triển)

**Response (501):**
```json
{
  "message": "Chức năng xuất PDF đang được phát triển"
}
```

---

## 🔄 Order Status Management

### PUT /api/orders/:id/status
Cập nhật trạng thái đơn hàng.

**Authentication:** Yêu cầu JWT token (Staff/Admin only)

**Path Parameters:**
- `id`: ID của đơn hàng (integer)

**Request Body:**
```json
{
  "status": "processing"
}
```

**Valid Status Values:**
- `pending`: Chờ xử lý
- `processing`: Đang xử lý
- `shipping`: Đang giao hàng
- `delivered`: Đã giao hàng
- `cancelled`: Đã hủy

**Response (200):**
```json
{
  "id": 1,
  "status": "processing",
  "updatedAt": "2024-01-15T11:30:00.000Z"
}
```

---

## 📊 Advanced Statistics APIs

### GET /api/statistics/revenue
Thống kê doanh thu theo thời gian.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Query Parameters:**
- `period`: "day", "week", "month", "year"
- `startDate` (optional): Ngày bắt đầu
- `endDate` (optional): Ngày kết thúc

**Response (200):**
```json
{
  "period": "month",
  "data": [
    {
      "date": "2024-01-01",
      "revenue": 1500000,
      "orders": 25
    },
    {
      "date": "2024-01-02",
      "revenue": 1800000,
      "orders": 30
    }
  ],
  "total": {
    "revenue": 45000000,
    "orders": 750
  }
}
```

### GET /api/statistics/customers
Thống kê khách hàng.

**Authentication:** Yêu cầu JWT token (Admin/Staff only)

**Response (200):**
```json
{
  "totalCustomers": 150,
  "newCustomers": 25,
  "returningCustomers": 125,
  "topCustomers": [
    {
      "id": 1,
      "fullName": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "totalOrders": 15,
      "totalSpent": 675000
    }
  ]
}
```

---

## 🔍 Search & Filter APIs

### GET /api/search
Tìm kiếm toàn bộ hệ thống.

**Authentication:** Yêu cầu JWT token

**Query Parameters:**
- `q`: Từ khóa tìm kiếm
- `type`: "products", "orders", "users" (Admin only)

**Response (200):**
```json
{
  "products": [
    {
      "id": 1,
      "name": "Phở Bò",
      "price": 45000,
      "image": "/uploads/pho-bo.jpg"
    }
  ],
  "orders": [
    {
      "id": 1,
      "customerName": "Nguyễn Văn A",
      "total": 90000,
      "status": "pending"
    }
  ]
}
```

---

## 📱 Mobile API Considerations

### Headers
Để tối ưu cho mobile app, các API hỗ trợ thêm headers:

```
X-Platform: mobile
X-App-Version: 1.0.0
X-Device-ID: unique-device-id
```

### Pagination
Các API danh sách hỗ trợ pagination:

**Query Parameters:**
- `page`: Số trang (default: 1)
- `limit`: Số items per page (default: 20, max: 100)

**Response Headers:**
```
X-Total-Count: 150
X-Page: 1
X-Per-Page: 20
X-Total-Pages: 8
```

---

## 🔐 Security Features

### Rate Limiting
- **Authentication endpoints**: 5 requests/minute
- **General APIs**: 100 requests/minute
- **Upload endpoints**: 10 requests/minute

### CORS Policy
```javascript
{
  origin: ["http://localhost:3000", "http://localhost:5000"],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"]
}
```

### Input Validation
Tất cả input được validate bằng Zod schemas:
- XSS protection
- SQL injection prevention
- File type validation
- Size limits

---

*Tài liệu API này cung cấp đầy đủ thông tin về tất cả endpoints trong hệ thống Na Food. Để test API, bạn có thể sử dụng Postman hoặc curl commands. Source code chi tiết có trong `server/routes.js`.*
