# NAFOODLVN Frontend

Frontend application cho hệ thống quản lý nhà hàng NAFOODLVN được xây dựng với React + Vite.

## Công nghệ sử dụng

- **React 18** - Thư viện UI chính
- **Vite** - Build tool và dev server
- **Tailwind CSS** - Framework CSS utility-first
- **Radix UI** - Component library accessible
- **React Hook Form** - Quản lý form
- **React Query** - State management cho API calls
- **Wouter** - Router nhẹ cho React
- **Framer Motion** - Animation library
- **Recharts** - Thư viện biểu đồ
- **Lucide React** - Icon library

## Cài đặt

1. Clone repository:
```bash
git clone https://github.com/Lamvanna/CONGNGHEPHANMEM.git
cd CONGNGHEPHANMEM
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Chạy development server:
```bash
npm run dev
```

4. Mở trình duyệt tại `http://localhost:5173`

## Scripts

- `npm run dev` - Chạy development server
- `npm run build` - Build production
- `npm run preview` - Preview production build

## Cấu trúc thư mục

```
src/
├── components/     # Các component tái sử dụng
├── pages/         # Các trang chính
├── hooks/         # Custom hooks
├── lib/           # Utilities và helpers
├── styles/        # CSS files
└── main.jsx       # Entry point
```

## Tính năng chính

- Giao diện quản lý nhà hàng hiện đại
- Responsive design
- Dark/Light theme
- Form validation
- Real-time updates
- Interactive charts và analytics

## License

MIT License
